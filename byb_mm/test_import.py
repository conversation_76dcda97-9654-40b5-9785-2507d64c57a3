#!/usr/bin/env python3
"""
测试导入是否正常工作
"""

import sys
import asyncio

def test_lark_msg_import():
    """测试 lark_msg 模块导入"""
    try:
        from lark_msg import send_lark
        print("✅ lark_msg 导入成功")
        return True
    except ImportError as e:
        print(f"❌ lark_msg 导入失败: {e}")
        return False

def test_byb_mm_aaa_import():
    """测试 byb_mm_aaa 模块导入"""
    try:
        # 只导入必要的部分，避免完整初始化
        import byb_mm_aaa
        print("✅ byb_mm_aaa 导入成功")
        return True
    except ImportError as e:
        print(f"❌ byb_mm_aaa 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  byb_mm_aaa 导入时出现其他错误: {e}")
        return False

def test_byb_mm_mana_import():
    """测试 byb_mm_mana 模块导入"""
    try:
        # 只导入必要的部分，避免完整初始化
        import byb_mm_mana
        print("✅ byb_mm_mana 导入成功")
        return True
    except ImportError as e:
        print(f"❌ byb_mm_mana 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  byb_mm_mana 导入时出现其他错误: {e}")
        return False

async def test_send_lark_function():
    """测试 send_lark 函数"""
    try:
        from lark_msg import send_lark
        # 不实际发送消息，只测试函数调用
        print("✅ send_lark 函数可以调用")
        return True
    except Exception as e:
        print(f"❌ send_lark 函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试导入...")
    print("=" * 50)
    
    results = []
    
    # 测试各个模块的导入
    results.append(test_lark_msg_import())
    results.append(test_byb_mm_aaa_import())
    results.append(test_byb_mm_mana_import())
    
    # 测试异步函数
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results.append(loop.run_until_complete(test_send_lark_function()))
        loop.close()
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        results.append(False)
    
    print("=" * 50)
    
    # 总结结果
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"🎉 所有测试通过! ({success_count}/{total_count})")
        print("循环导入问题已解决，可以正常使用 send_lark 功能")
        return 0
    else:
        print(f"⚠️  部分测试失败: {success_count}/{total_count}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
