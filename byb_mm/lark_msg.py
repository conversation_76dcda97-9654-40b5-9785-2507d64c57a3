import backoff
import json
import aiohttp
import asyncio
import logging

# 创建独立的日志记录器，避免循环导入
logger = logging.getLogger(__name__)



@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='warning'):
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"{level}:"+str(msg)
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    if logger is not None:
                        logger.error(f"{res}")
    except:
        logger.error(f"{msg} 报警错误")


asyncio.run(send_lark('test message'))